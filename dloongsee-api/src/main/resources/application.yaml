server:
  port: 58654
  servlet:
    context-path: /wutos/dloongsee

spring:
  datasource:
    druid:
      url: ******************************************************************************************************************
      username: root
      password: dNVKbNCyC78n
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 1
      max-active: 24
      min-idle: 2
      max-wait: 1000
      validation-query: SELECT 1
      validation-query-timeout: 1000
  influx:
    database: dloongsee_jga
    url: http://************:8086
    user: admin
    password: admin
  redis:
    host: ************
    port: 6379
    password: wutos
    database: 12

zmq:
  track:
    servers:
      - url: tcp://***********:9031
        direction: UP
      - url: tcp://***********:9030
        direction: DOWN
  event:
    algorithms:
      - name: algorithms1
        desc: 驾驶类事件
        types: [ BACK_DRIVE,EMERGENCY_LANE,OVER_SPEED,LOW_SPEED,STOP ]
        enabled: true
        servers:
          - url: tcp://localhost:9032
            topic: 0x02,0x00,0x00,0x00
            direction: DOWN
          - url: tcp://localhost:9033
            topic: 0x02,0x00,0x00,0x00
            direction: UP
      - name: algorithms2
        desc: 行人入侵事件
        types: [ PEDESTRIAN ]
        enabled: true
        servers:
          - url: tcp://localhost:8901
            topic: 0x09,0x00,0x00,0x00
            direction: UP
          - url: tcp://localhost:8885
            topic: 0x09,0x00,0x00,0x00
            direction: DOWN
      - name: algorithms3
        desc: 抛洒物事件
        types: [ SPRINKLE ]
        enabled: true
        servers:
          - url: tcp://localhost:8001
            topic: 0x0A,0x00,0x00,0x00
            direction: UP
          - url: tcp://localhost:9885
            topic: 0x0A,0x00,0x00,0x00
            direction: DOWN

milDir: l2r