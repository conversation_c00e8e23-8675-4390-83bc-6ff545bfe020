package com.wutos.dloongsee.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wutos.dloongsee.api.entity.PassEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Mapper
public interface PassEventMapper extends BaseMapper<PassEvent> {
    /**
     * 根据eventId 更新endTime ,endMil
     *
     * @param endTime
     * @param endMil
     * @param eventId
     * @return
     */
    Integer updateEventEndTimeAndEndMil(@Param("endTime") LocalDateTime endTime, @Param("endMil") Integer endMil, @Param("eventId") String eventId);
}
