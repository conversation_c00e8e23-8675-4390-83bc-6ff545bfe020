package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("traffic_event")
public class TrafficEvent {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事件 id
     */
    private String eventId;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 方向
     */
    private RoadDirection direction;

    /**
     * 开始里程
     */
    private Integer startMil;

    /**
     * 结束里程
     */
    private Integer endMil;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private  LocalDateTime endTime;

    /**
     * 处置时间
     */
    private LocalDateTime disposeTime;

    /**
     * 处置状态
     */
    private Integer disposeStatus;

    /**
     * 路段 id
     */
    private Integer segmentId;
}
