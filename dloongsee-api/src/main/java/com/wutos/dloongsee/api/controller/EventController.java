package com.wutos.dloongsee.api.controller;

import com.wutos.dloongsee.api.controller.reqeust.EventRequest;
import com.wutos.dloongsee.api.service.EventService;
import com.wutos.dloongsee.api.vo.PageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/event")
public class EventController {

    @Autowired
    private EventService eventService;

    @GetMapping("/page/")
    public PageResponse<?> getEvent(@RequestBody EventRequest eventRequest) {
        return PageResponse.of(eventService.findByRequest(eventRequest));
    }
}
