package com.wutos.dloongsee.api.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wutos.dloongsee.api.entity.CarTrack;
import com.wutos.dloongsee.api.entity.RoadRamp;
import com.wutos.dloongsee.api.mapper.CarTrackMapper;
import com.wutos.dloongsee.api.mapper.RoadRampMapper;
import com.wutos.dloongsee.api.websocket.CarTrackWebsocket;
import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.enums.RampType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.springevent.ZMQCarTrackEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CarTrackService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CarTrackMapper carTrackMapper;

    @Autowired
    private RoadRampMapper roadRampMapper;

    @Autowired
    private CarTrackWebsocket carTrackWebsocket;

    @Autowired
    private ThreadPoolTaskExecutor carTrackThreadPoolTaskExecutor;

    private static List<RoadRamp> ROAD_RAMPS;


    public static final String CAR_TRACK_REDIS_PREFIX = "carTrack:";
    private static final String CAR_TRACK_TIME_REDIS_PREFIX = "carTrack:time:";

    private volatile List<ZMQCarTrackDto> lastTrackUp = Collections.emptyList();
    private volatile List<ZMQCarTrackDto> lastTrackDown = Collections.emptyList();
    private final List<ZMQCarTrackDto> inOrOutUp = new CopyOnWriteArrayList<>();
    private final List<ZMQCarTrackDto> inOrOutDown = new CopyOnWriteArrayList<>();

    @PostConstruct
    public void init() {
        // 出入口
        ROAD_RAMPS = roadRampMapper.selectList(null);
    }

    @EventListener
    public void processCarTrackData(ZMQCarTrackEvent zmqCarTrackEvent) {
        // 过滤掉可能存在的 null 值
        List<ZMQCarTrackDto> zmqCarTrackDtoList = zmqCarTrackEvent.getZmqCarTrackDtoList().stream().filter(Objects::nonNull).collect(Collectors.toList());

        if (zmqCarTrackDtoList.isEmpty()) {
            log.warn("实时轨迹服务：接收到空车辆轨迹数据");
            return;
        }
        log.debug("实时轨迹服务：接收到车辆轨迹数据大小：{}", zmqCarTrackDtoList.size());

        // 按车辆id缓存轨迹
        CompletableFuture.runAsync(() -> {
            redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                long now = System.currentTimeMillis();
                for (ZMQCarTrackDto dto : zmqCarTrackDtoList) {
                    connection.set(
                            (CAR_TRACK_TIME_REDIS_PREFIX + dto.getId()).getBytes(),
                            String.valueOf(now).getBytes()
                    );
                    connection.rPush(
                            (CAR_TRACK_REDIS_PREFIX + dto.getId()).getBytes(),
                            redisTemplate.getValueSerializer().serialize(dto)
                    );
                }
                return null;
            });
        }, carTrackThreadPoolTaskExecutor);

        List<ZMQCarTrackDto> inOrOut = zmqCarTrackDtoList.stream().filter(e -> e.isComeIn() || e.isGetOut()).collect(Collectors.toList());

        if (RoadDirection.UP == zmqCarTrackDtoList.get(0).getRoadDirection()) {
            lastTrackUp = zmqCarTrackDtoList;
            if (!inOrOut.isEmpty()) {
                inOrOutUp.addAll(inOrOut);
            }
        } else {
            lastTrackDown = zmqCarTrackDtoList;
            if (!inOrOut.isEmpty()) {
                inOrOutDown.addAll(inOrOut);
            }
        }
    }

    /**
     * 轨迹数据降频
     */
    @Scheduled(fixedDelay = 1000)
    public void sendLastMessage() {
        List<ZMQCarTrackDto> up = processDirectionalTracks(lastTrackUp, inOrOutUp);
        List<ZMQCarTrackDto> down = processDirectionalTracks(lastTrackDown, inOrOutDown);
        resetCache();
        List<ZMQCarTrackDto> union = Stream.concat(up.stream(), down.stream()).collect(Collectors.toList());
        carTrackWebsocket.sendMessage(union);
    }

    /**
     * 轨迹结束后存入数据库
     * @throws JsonProcessingException
     */
    @Scheduled(cron = "0/30 * * * * *")
    public void checkCarTrackExpire() throws JsonProcessingException {
        // 60s未收到轨迹则该车辆轨迹已结束
        Set<String> keys = redisTemplate.keys(CAR_TRACK_TIME_REDIS_PREFIX + "*");
        for (String key : keys) {
            Long time = (Long) redisTemplate.opsForValue().get(key);
            if (time == null) {
                log.warn("轨迹时间戳为空，删除key: {}", key);
                redisTemplate.delete(key);
                continue;
            }
            if (System.currentTimeMillis() - time < 60 * 1000) {
                continue;
            }
            String[] split = key.split(":");
            String id = split[split.length - 1];
            List<ZMQCarTrackDto> tracks = redisTemplate.opsForList().range(CAR_TRACK_REDIS_PREFIX + id, 0, -1);

            redisTemplate.delete(key);
            redisTemplate.delete(CAR_TRACK_REDIS_PREFIX + id);

            CarTrack carTrack = null;
            try {
                carTrack = buildTrack(tracks);
            } catch (Exception e) {
                log.error("构造单车轨迹失败", e);
            }
            if (carTrack != null) {
                carTrackMapper.insert(carTrack);
            }
        }
    }

    private CarTrack buildTrack(List<ZMQCarTrackDto> tracks) {

        CarTrack track = new CarTrack();
        String id = tracks.get(0).getId();
        track.setCarId(id);
        track.setDirection(tracks.get(0).getRoadDirection());
        track.setStartTime(Instant.ofEpochMilli(tracks.get(0).getMessageTicks()).atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
        track.setEndTime(Instant.ofEpochMilli(tracks.get(tracks.size() - 1).getMessageTicks()).atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());

        // 默认车信息
        track.setCarNum(tracks.get(tracks.size() - 1).getCn());
        track.setCarType(tracks.get(0).getCarType());
        track.setCarWeight(2 + Math.floor(58 * Math.random()));

        // 出入口
        RoadRamp inRoadRamp = getNearPosition(tracks.get(0).getMil(), track.getDirection(), RampType.IN);
        RoadRamp outRoadRamp = getNearPosition(tracks.get(tracks.size() - 1).getMil(), track.getDirection(), RampType.OUT);
        track.setStartPoint(inRoadRamp.getId());
        track.setEndPoint(outRoadRamp.getId());

        // 速度
        float speedSum = tracks.get(0).getSpeed();
        float speedMax = tracks.get(0).getSpeed();
        float speedMin = tracks.get(0).getSpeed();
        for (int i = 1; i < tracks.size(); i++) {
            Float speed = tracks.get(i).getSpeed();
            speedSum += speed;
            if (speed > speedMax) {
                speedMax = speed;
            }
            if (speed < speedMin) {
                speedMin = speed;
            }
        }
        float speedAvg = speedSum / tracks.size();
        track.setSpeedAvg((double) speedAvg);
        track.setSpeedMax((double) speedMax);
        track.setSpeedMin((double) speedMin);

        return track;
    }

    public static RoadRamp getNearPosition(int point, RoadDirection direction, RampType type) {
        List<RoadRamp> list = ROAD_RAMPS.stream()
                .filter(e -> e.getType() == type)
                .filter(e -> e.getDirection() == direction)
                .collect(Collectors.toList());
        int delta = Math.abs(list.get(0).getMil() - point);
        RoadRamp r = null;
        for (RoadRamp roadRamp : list) {
            int abs = Math.abs(roadRamp.getMil() - point);
            if (abs <= delta) {
                delta = abs;
                r = roadRamp;
            }
        }
        return r;
    }

    /**
     * 处理单个方向的轨迹数据（过滤和合并）
     * @param lastTrack 最新的一帧轨迹
     * @param inOrOut   周期内累积的上下匝道轨迹
     * @return 合并后的最终轨迹列表
     */
    private List<ZMQCarTrackDto> processDirectionalTracks(List<ZMQCarTrackDto> lastTrack, List<ZMQCarTrackDto> inOrOut) {
        if (inOrOut.isEmpty()) {
            return lastTrack;
        }
        Set<String> inOrOutIds = inOrOut.stream().map(ZMQCarTrackDto::getId).collect(Collectors.toSet());
        // 过滤掉最新一帧中，已经记录了进出匝道的车辆
        List<ZMQCarTrackDto> filteredTrack = lastTrack.stream().filter(e -> !inOrOutIds.contains(e.getId())).collect(Collectors.toList());
        // 将过滤后的主路轨迹和匝道轨迹合并
        filteredTrack.addAll(inOrOut);
        return filteredTrack;
    }

    /**
     * 重置缓存
     */
    private void resetCache() {
        lastTrackUp = Collections.emptyList();
        lastTrackDown = Collections.emptyList();
        inOrOutUp.clear();
        inOrOutDown.clear();
    }
}
