package com.wutos.dloongsee.api.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 分页数据响应
 * @param <T>
 */
@Data
@AllArgsConstructor
public class PageResponse<T> {

    private long total;
    private long page;
    private long size;
    private List<T> records;

    public static <T> PageResponse<T> of(Page<T> page) {
        return new PageResponse<>(
                page.getTotal(),
                page.getCurrent() + 1,
                page.getSize(),
                page.getRecords()
        );
    }
}
