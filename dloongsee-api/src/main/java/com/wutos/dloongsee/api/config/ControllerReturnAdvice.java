package com.wutos.dloongsee.api.config;

import com.wutos.dloongsee.api.vo.BaseResponse;
import com.wutos.dloongsee.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@Slf4j
@RestControllerAdvice(annotations = RestController.class)
public class ControllerReturnAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (body instanceof BaseResponse) {
            return body;
        }
        return BaseResponse.ok(body);
    }

    // 业务异常
    @ExceptionHandler(value = BizException.class)
    public BaseResponse<Void> errorHandler(BizException e) {
        log.error("业务异常", e);
        return BaseResponse.error(e.getMessage());
    }

    // 系统异常
    @ExceptionHandler(value = Exception.class)
    public BaseResponse<Void> errorHandler(Exception e) {
        log.error("系统异常", e);
        return BaseResponse.error("系统异常");
    }
}