package com.wutos.dloongsee.api.controller.reqeust;

import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import lombok.Data;

import java.time.LocalDate;

@Data
public class EventRequest {
    /**
     * 事件大类：流量失衡、违规驾驶、通行障碍
     */
    private EventCategory category;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private  LocalDate endTime;

    /**
     * 路段 id
     */
    private Integer segmentId;

    private Integer pageSize;

    private Integer pageNum;
}
