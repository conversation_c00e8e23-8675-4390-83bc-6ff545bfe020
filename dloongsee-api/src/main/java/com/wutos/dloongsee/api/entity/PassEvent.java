package com.wutos.dloongsee.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("pass_event")
public class PassEvent {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 算法输出事件id
     */
    private String eventId;
    /**
     * 事件类型
     */
    private EventType eventType;
    /**
     * 开始里程
     */
    private Integer startMil;
    /**
     * 结束里程
     */
    private Integer endMil;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 处置时间
     */
    private LocalDateTime disposeTime;
    /**
     * 处置状态
     */
    private Integer disposeStatus;
    /**
     * 路段id
     */
    private Integer segmentId;
    /**
     * 方向 枚举
     */
    private RoadDirection direction;
    /**
     * 车道
     */
    private Integer wn;
}
