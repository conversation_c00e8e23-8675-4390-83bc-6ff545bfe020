package com.wutos.dloongsee.api.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wutos.dloongsee.api.components.GeoComponent;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.mapper.RoadSegmentMapper;
import com.wutos.dloongsee.api.service.CarTrackService;
import com.wutos.dloongsee.api.vo.CarTrackVo;
import com.wutos.dloongsee.common.dto.ZMQCarTrackDto;
import com.wutos.dloongsee.common.enums.RampType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

@Slf4j
@Component
@ServerEndpoint(value = "/grid/rttracesubscribe")
public class CarTrackWebsocket {

    @Autowired
    private RoadSegmentMapper roadSegmentMapper;

    @Autowired
    private GeoComponent geoComponent;

    @Autowired
    private ObjectMapper objectMapper;

    private Map<Integer, RoadSegment> ROAD_SEGMENTS;

    // 存储每个路段ID对应的WebSocket会话列表
    private static final Map<Integer, CopyOnWriteArrayList<Session>> roadSegmentSessions = new ConcurrentHashMap<>();

    @PostConstruct
    public  void init() {
        List<RoadSegment> roadSegments= roadSegmentMapper.selectList(null);
        ROAD_SEGMENTS = roadSegments.stream().collect(Collectors.toMap(RoadSegment::getId, e -> e));
    }

    @OnOpen
    public void onOpen(Session session) {
        // 将路段ID存储到会话属性中
        String query = session.getQueryString();
        Map<String, Integer> params = parseQuery(query);
        Integer roadSegmentId = params.get("roadSegmentId");
        session.getUserProperties().put("roadSegmentId", roadSegmentId);
        roadSegmentSessions.computeIfAbsent(roadSegmentId, k -> new CopyOnWriteArrayList<>()).add(session);

        log.info("实时轨迹websocket连接建立成功，路段ID：{}", roadSegmentId);
    }

    @OnClose
    public void onClose(Session session) {
        Integer roadSegmentId = (Integer) session.getUserProperties().get("roadSegmentId");
        if (roadSegmentId != null) {
            CopyOnWriteArrayList<Session> sessions = roadSegmentSessions.get(roadSegmentId);
            if (sessions != null) {
                sessions.remove(session);
                if (sessions.isEmpty()) {
                    roadSegmentSessions.remove(roadSegmentId);
                }
                log.info("实时轨迹websocket连接关闭，路段ID：{}, 剩余连接数：{}",
                        roadSegmentId, sessions.size());
            }
        }
    }

    @OnError
    public void onError(Session session, Throwable error) {
        Integer roadSegmentId = (Integer) session.getUserProperties().get("roadSegmentId");
        log.error("实时轨迹WebSocket发生错误，路段ID：{}, 错误信息：{}", roadSegmentId, error.getMessage());
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        Integer roadSegmentId = (Integer) session.getUserProperties().get("roadSegmentId");
        log.info("实时轨迹websocket收到消息，路段ID：{}, 消息：{}", roadSegmentId, message);
    }

    /**
     * 向所有客户端发送消息
     */
    public void sendMessage(List<ZMQCarTrackDto> zmqCarTrackDtoList) {
        for (CopyOnWriteArrayList<Session> sessions : roadSegmentSessions.values()) {
            for (Session session : sessions) {
                try {
                    if (session.isOpen()) {
                        List<CarTrackVo> carTrackVos;
                        if (session.getUserProperties().containsKey("roadSegmentId")) {
                            Integer roadSegmentId = (Integer) session.getUserProperties().get("roadSegmentId");
                            if (roadSegmentId != -1) {
                                carTrackVos = zmqCarTrackDtoList.stream()
                                        .filter(zmqCarTrackDto ->
                                                zmqCarTrackDto.getMil() >= ROAD_SEGMENTS.get(roadSegmentId).getStartMil() && zmqCarTrackDto.getMil() <= ROAD_SEGMENTS.get(roadSegmentId).getEndMil())
                                        .map(zmqCarTrackDto -> {
                                            int mil = zmqCarTrackDto.getMil();
                                            RoadDirection direction = zmqCarTrackDto.getRoadDirection();
                                            Integer rampId = null;
                                            if (zmqCarTrackDto.isGetOut()) {
                                                rampId = CarTrackService.getNearPosition(mil, direction, RampType.OUT).getId();
                                            }
                                            if (zmqCarTrackDto.isComeIn()) {
                                                rampId = CarTrackService.getNearPosition(mil, direction, RampType.IN).getId();
                                            }
                                            return CarTrackVo.builder()
                                                    .id(zmqCarTrackDto.getId())
                                                    .cn(zmqCarTrackDto.getCn())
                                                    .type(zmqCarTrackDto.getCarType())
                                                    .wn(zmqCarTrackDto.getWn())
                                                    .mil(mil)
                                                    .speed(zmqCarTrackDto.getSpeed())
                                                    .direction(direction)
                                                    .getOut(zmqCarTrackDto.isGetOut())
                                                    .comeIn(zmqCarTrackDto.isComeIn())
                                                    .rampId(rampId)
                                                    .build();
                                        })
                                        .collect(Collectors.toList());
                            } else {
                                carTrackVos = zmqCarTrackDtoList.stream()
                                        .map(zmqCarTrackDto ->CarTrackVo.builder()
                                                .id(zmqCarTrackDto.getId())
                                                .wn(zmqCarTrackDto.getWn())
                                                .direction(zmqCarTrackDto.getRoadDirection())
                                                .lngLat(geoComponent.milToLngLat(zmqCarTrackDto.getMil()))
                                                .build()).collect(Collectors.toList());
                            }
                            session.getAsyncRemote().sendText(objectMapper.writeValueAsString(carTrackVos));
                        }
                    }
                } catch (Exception e) {
                    log.error("发送消息失败：{}", e.getMessage());
                }
            }
        }
    }

    private Map<String, Integer> parseQuery(String query) {
        Map<String, Integer> map = new HashMap<>();
        if (query != null) {
            for (String param : query.split("&")) {
                String[] pair = param.split("=");
                if (pair.length > 1) {
                    map.put(pair[0], Integer.parseInt(pair[1]));
                }
            }
        }
        return map;
    }
}
