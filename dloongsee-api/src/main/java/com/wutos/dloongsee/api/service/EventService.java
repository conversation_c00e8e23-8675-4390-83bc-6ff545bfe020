package com.wutos.dloongsee.api.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wutos.dloongsee.api.controller.reqeust.EventRequest;
import com.wutos.dloongsee.api.entity.DriveEvent;
import com.wutos.dloongsee.api.entity.PassEvent;
import com.wutos.dloongsee.api.entity.RoadSegment;
import com.wutos.dloongsee.api.entity.TrafficEvent;
import com.wutos.dloongsee.api.mapper.DriveEventMapper;
import com.wutos.dloongsee.api.mapper.PassEventMapper;
import com.wutos.dloongsee.api.mapper.RoadSegmentMapper;
import com.wutos.dloongsee.api.mapper.TrafficEventMapper;
import com.wutos.dloongsee.common.dto.ZMQEventDTO;
import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import com.wutos.dloongsee.common.springevent.ZMQEventSpringEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 事件
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/16
 */
@Service
@Slf4j
public class EventService {

    private final DriveEventMapper driveEventMapper;
    private final RoadSegmentMapper roadSegmentMapper;
    private final PassEventMapper passEventMapper;
    private final TrafficEventMapper trafficEventMapper;

    public EventService(DriveEventMapper driveEventMapper, RoadSegmentMapper roadSegmentMapper,
                       PassEventMapper passEventMapper, TrafficEventMapper trafficEventMapper) {
        this.driveEventMapper = driveEventMapper;
        this.roadSegmentMapper = roadSegmentMapper;
        this.passEventMapper = passEventMapper;
        this.trafficEventMapper = trafficEventMapper;
    }

    @Async("eventThreadPoolTaskExecutor")
    @EventListener
    public void processEvent(ZMQEventSpringEvent zmqEventSpringEvent) {
        ZMQEventDTO zmqEventDTO = zmqEventSpringEvent.getZmqEventDTO();
        EventType eventType = zmqEventDTO.getType();
        switch (eventType.getCategory()) {
            //驾驶异常
            case DRIVE:
                if (zmqEventDTO.getIsStarted()) {
                    DriveEvent driveEvent = this.generateDriveEvent(zmqEventDTO, eventType);
                    driveEventMapper.insert(driveEvent);
                } else {
                    //事件结束更新
                    this.updateDriveEvent(zmqEventDTO);
                }
                break;
            case PASS:
                //通行障碍
                if (zmqEventDTO.getIsStarted()) {
                    PassEvent passEvent = this.generatePassEvent(zmqEventDTO, eventType);
                    passEventMapper.insert(passEvent);
                } else {
                    //事件结束更新
                    this.updatePassEvent(zmqEventDTO);
                }
                break;
            case TRAFFIC:
                //流量异常
                if (zmqEventDTO.getIsStarted()) {
                    TrafficEvent trafficEvent = this.generateTrafficEvent(zmqEventDTO, eventType);
                    trafficEventMapper.insert(trafficEvent);
                } else {
                    //事件结束更新
                    this.updateTrafficEvent(zmqEventDTO);
                }
                break;
            default:
                log.error("类型匹配失败，当前事件类型：{}", eventType.getCategory());
        }

    }

    /**
     * 构建DriveEvent 实体类
     *
     * @param zmqEventDTO
     * @param eventType
     * @return
     */
    private DriveEvent generateDriveEvent(ZMQEventDTO zmqEventDTO, EventType eventType) {
        DriveEvent driveEvent = new DriveEvent();
        driveEvent.setCarId(zmqEventDTO.getCarId());
        driveEvent.setEventId(zmqEventDTO.getEventId());
        driveEvent.setEventType(eventType);
        driveEvent.setCarNum(zmqEventDTO.getCarNum());
        driveEvent.setStartMil(zmqEventDTO.getStartMil());
        driveEvent.setEndMil(zmqEventDTO.getEndMil());
        driveEvent.setStartTime(zmqEventDTO.getStartTime());
        driveEvent.setEndTime(zmqEventDTO.getEndTime());
        driveEvent.setSegmentId(this.getRoadSegmentIdByMil(zmqEventDTO.getStartMil()));
        driveEvent.setDirection(zmqEventDTO.getDirection() == 0 ? RoadDirection.UP : RoadDirection.DOWN);
        driveEvent.setWn(zmqEventDTO.getLane());
        return driveEvent;
    }

    private PassEvent generatePassEvent(ZMQEventDTO zmqEventDTO, EventType eventType) {
        PassEvent passEvent = new PassEvent();
        passEvent.setEventId(zmqEventDTO.getEventId());
        passEvent.setEventType(eventType);
        passEvent.setStartMil(zmqEventDTO.getStartMil());
        passEvent.setEndMil(zmqEventDTO.getEndMil());
        passEvent.setStartTime(zmqEventDTO.getStartTime());
        passEvent.setEndTime(zmqEventDTO.getEndTime());
        passEvent.setSegmentId(this.getRoadSegmentIdByMil(zmqEventDTO.getStartMil()));
        passEvent.setDirection(zmqEventDTO.getDirection() == 0 ? RoadDirection.UP : RoadDirection.DOWN);
        passEvent.setWn(zmqEventDTO.getLane());
        return passEvent;
    }

    private TrafficEvent generateTrafficEvent(ZMQEventDTO zmqEventDTO, EventType eventType) {
        TrafficEvent trafficEvent = new TrafficEvent();
        trafficEvent.setEventId(zmqEventDTO.getEventId());
        trafficEvent.setEventType(eventType);
        trafficEvent.setStartMil(zmqEventDTO.getStartMil());
        trafficEvent.setEndMil(zmqEventDTO.getEndMil());
        trafficEvent.setStartTime(zmqEventDTO.getStartTime());
        trafficEvent.setEndTime(zmqEventDTO.getEndTime());
        trafficEvent.setSegmentId(this.getRoadSegmentIdByMil(zmqEventDTO.getStartMil()));
        trafficEvent.setDirection(zmqEventDTO.getDirection() == 0 ? RoadDirection.UP : RoadDirection.DOWN);
        return trafficEvent;
    }


    /**
     * 事件更新
     *
     * @param zmqEventDTO
     */
    private void updateDriveEvent(ZMQEventDTO zmqEventDTO) {
        LambdaQueryWrapper<DriveEvent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DriveEvent::getEventId, zmqEventDTO.getEventId());
        DriveEvent driveEvent = driveEventMapper.selectOne(lambdaQueryWrapper);
        driveEventMapper.updateEventEndTimeAndEndMil(zmqEventDTO.getEndTime(), zmqEventDTO.getEndMil(), driveEvent.getEventId());
    }

    /**
     * 事件更新
     *
     * @param zmqEventDTO
     */
    private void updatePassEvent(ZMQEventDTO zmqEventDTO) {
        LambdaQueryWrapper<PassEvent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PassEvent::getEventId, zmqEventDTO.getEventId());
        PassEvent passEvent = passEventMapper.selectOne(lambdaQueryWrapper);
        passEventMapper.updateEventEndTimeAndEndMil(zmqEventDTO.getEndTime(), zmqEventDTO.getEndMil(), passEvent.getEventId());
    }

    /**
     * 事件更新
     *
     * @param zmqEventDTO
     */
    private void updateTrafficEvent(ZMQEventDTO zmqEventDTO) {
        LambdaQueryWrapper<TrafficEvent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TrafficEvent::getEventId, zmqEventDTO.getEventId());
        TrafficEvent trafficEvent = trafficEventMapper.selectOne(lambdaQueryWrapper);
        trafficEventMapper.updateEventEndTimeAndEndMil(zmqEventDTO.getEndTime(), zmqEventDTO.getEndMil(), trafficEvent.getEventId());
    }

    /**
     * 根据开始里程获取路段id
     *
     * @param startMil
     * @return
     */
    private Integer getRoadSegmentIdByMil(Integer startMil) {
        Integer roadSegmentId = 0;
        List<RoadSegment> roadSegmentList = roadSegmentMapper.selectList(new QueryWrapper<>());
        for (RoadSegment roadSegment : roadSegmentList) {
            if (startMil >= roadSegment.getStartMil() && startMil <= roadSegment.getEndMil()) {
                roadSegmentId = roadSegment.getId();
                break;
            }
        }
        return roadSegmentId;
    }

    /**
     * 根据条件分页查询事件数据
     *
     * @param eventRequest 查询条件
     * @return 分页查询结果
     */
    public Page<?> findByRequest(EventRequest eventRequest) {
        EventCategory eventCategory = eventRequest.getCategory();
        Page<?> page = new Page<>(eventRequest.getPageNum(), eventRequest.getPageSize());

        switch (eventCategory) {
            case DRIVE:
                return queryEvents(eventRequest, page, driveEventMapper,
                    DriveEvent::getEventType, DriveEvent::getSegmentId, DriveEvent::getStartTime);
            case PASS:
                return queryEvents(eventRequest, page, passEventMapper,
                    PassEvent::getEventType, PassEvent::getSegmentId, PassEvent::getStartTime);
            case TRAFFIC:
                return queryEvents(eventRequest, page, trafficEventMapper,
                    TrafficEvent::getEventType, TrafficEvent::getSegmentId, TrafficEvent::getStartTime);
            default:
                log.error("类型匹配失败，当前事件类型：{}", eventCategory);
                throw new IllegalArgumentException("不支持的事件类型：" + eventCategory);
        }
    }

    /**
     * 通用事件查询方法
     *
     * @param eventRequest 查询条件
     * @param page 分页对象
     * @param mapper 对应的Mapper
     * @param eventTypeGetter 事件类型字段getter
     * @param segmentIdGetter 路段ID字段getter
     * @param startTimeGetter 开始时间字段getter
     * @param <T> 事件实体类型
     * @return 查询结果
     */
    private <T> Page<T> queryEvents(EventRequest eventRequest, Page<?> page, BaseMapper<T> mapper,
                                   Function<T, EventType> eventTypeGetter,
                                   Function<T, Integer> segmentIdGetter,
                                   Function<T, LocalDateTime> startTimeGetter) {
        LambdaQueryWrapper<T> queryWrapper = new LambdaQueryWrapper<>();

        // 事件类型条件
        if (eventRequest.getEventType() != null) {
            queryWrapper.eq(eventTypeGetter, eventRequest.getEventType());
        }

        // 路段ID条件
        if (eventRequest.getSegmentId() != null) {
            queryWrapper.eq(segmentIdGetter, eventRequest.getSegmentId());
        }

        // 时间范围条件
        if (eventRequest.getStartTime() != null) {
            queryWrapper.ge(startTimeGetter, eventRequest.getStartTime().atStartOfDay());
        }
        if (eventRequest.getEndTime() != null) {
            queryWrapper.le(startTimeGetter, eventRequest.getEndTime().atTime(23, 59, 59));
        }

        // 按开始时间倒序排列
        queryWrapper.orderByDesc(startTimeGetter);

        return mapper.selectPage((Page<T>) page, queryWrapper);
    }
}


