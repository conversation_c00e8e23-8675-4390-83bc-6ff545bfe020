package com.wutos.dloongsee.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wutos.dloongsee.common.enums.EventType;
import com.wutos.dloongsee.common.enums.RoadDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZMQEventDTO {
    /**
     * 事件id
     */
    private String eventId;

    /**
     * 车辆id
     */
    private String carId;

    /**
     * 车牌
     */
    private String carNum;

    /**
     * 车道号
     */
    private Integer lane;

    /**
     * 事件开始时间
     */
    private LocalDateTime startTime;

    /**
     * 事件终止时间
     */
    private LocalDateTime endTime;

    /**
     * 事件起始位置
     */
    public Integer startMil;

    /**
     * 事件终止位置
     */
    public Integer endMil;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 方向 0左 1右
     */
    private Integer direction;

    /**
     * 事件类型
     */
    private EventType type;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 是否事件开始
     */
    private Boolean isStarted;

    /**
     * 上下行 up down
     */
    @JsonIgnore
    public RoadDirection getRoadDirection() {
        String[] split = eventId.split("@");
        return RoadDirection.valueOf(split[split.length - 1].toUpperCase());
    }
}
