package com.wutos.dloongsee.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 事件类型
 * <p>
 *
 * <AUTHOR>
 * @since 2025/8/16
 */
@Getter
@AllArgsConstructor
public enum EventType {
    /**
     * 逆行
     */
    BACK_DRIVE(EventCategory.DRIVE),
    /**
     * 占用应急
     */
    EMERGENCY_LANE(EventCategory.DRIVE),
    /**
     * 超速
     */
    OVER_SPEED(EventCategory.DRIVE),
    /**
     * 低速
     */
    LOW_SPEED(EventCategory.DRIVE),
    /**
     * 停车
     */
    STOP(EventCategory.DRIVE),

    /**
     * 行人
     */
    PEDESTRIAN(EventCategory.PASS),
    /**
     * 抛洒物
     */
    SPRINKLE(EventCategory.PASS),

    /**
     * 主线拥堵
     */
    CONGESTION_MAIN(EventCategory.TRAFFIC);

    private final EventCategory category;

}
