import com.wutos.dloongsee.api.controller.reqeust.EventRequest;
import com.wutos.dloongsee.api.service.EventService;
import com.wutos.dloongsee.common.enums.EventCategory;
import com.wutos.dloongsee.common.enums.EventType;

import java.time.LocalDate;

/**
 * EventService 测试示例
 * 这个文件展示了如何使用重构后的 EventService
 */
public class EventServiceTest {
    
    public void testQueryDriveEvents() {
        // 创建查询请求
        EventRequest request = new EventRequest();
        request.setCategory(EventCategory.DRIVE);
        request.setEventType(EventType.OVER_SPEED); // 查询超速事件
        request.setStartTime(LocalDate.of(2025, 8, 1));
        request.setEndTime(LocalDate.of(2025, 8, 20));
        request.setSegmentId(1); // 路段ID
        request.setPageNum(1);
        request.setPageSize(10);
        
        // 调用查询方法
        // Page<?> result = eventService.findByRequest(request);
        
        System.out.println("查询驾驶异常事件 - 超速");
    }
    
    public void testQueryPassEvents() {
        EventRequest request = new EventRequest();
        request.setCategory(EventCategory.PASS);
        request.setEventType(EventType.PEDESTRIAN); // 查询行人入侵事件
        request.setStartTime(LocalDate.of(2025, 8, 1));
        request.setEndTime(LocalDate.of(2025, 8, 20));
        request.setPageNum(1);
        request.setPageSize(20);
        
        // Page<?> result = eventService.findByRequest(request);
        
        System.out.println("查询通行障碍事件 - 行人入侵");
    }
    
    public void testQueryTrafficEvents() {
        EventRequest request = new EventRequest();
        request.setCategory(EventCategory.TRAFFIC);
        request.setEventType(EventType.CONGESTION_MAIN); // 查询主线拥堵事件
        request.setStartTime(LocalDate.of(2025, 8, 1));
        request.setEndTime(LocalDate.of(2025, 8, 20));
        request.setPageNum(1);
        request.setPageSize(15);
        
        // Page<?> result = eventService.findByRequest(request);
        
        System.out.println("查询流量异常事件 - 主线拥堵");
    }
    
    public void testQueryAllDriveEvents() {
        // 查询所有驾驶异常事件，不指定具体类型
        EventRequest request = new EventRequest();
        request.setCategory(EventCategory.DRIVE);
        // 不设置 eventType，查询所有驾驶异常事件
        request.setPageNum(1);
        request.setPageSize(50);
        
        // Page<?> result = eventService.findByRequest(request);
        
        System.out.println("查询所有驾驶异常事件");
    }
}
