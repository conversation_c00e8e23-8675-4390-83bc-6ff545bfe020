-- =====================================================
-- 事件管理系统测试数据脚本
-- 用于验证 EventController 分页查询接口
-- =====================================================

-- 清理现有测试数据（可选）
-- DELETE FROM drive_event WHERE event_id LIKE 'TEST_%';
-- DELETE FROM pass_event WHERE event_id LIKE 'TEST_%';
-- DELETE FROM traffic_event WHERE event_id LIKE 'TEST_%';
-- DELETE FROM road_segment WHERE name LIKE '测试路段%';

-- =====================================================
-- 1. 插入路段基础数据
-- =====================================================
INSERT INTO road_segment (id, name, lane_count, start_mil, end_mil, lnglats, has_emergency_lane) VALUES
(1, '测试路段A', 4, 1000, 2000, '[[116.3974,39.9093],[116.3984,39.9103]]', 1),
(2, '测试路段B', 3, 2000, 3000, '[[116.3984,39.9103],[116.3994,39.9113]]', 0),
(3, '测试路段C', 5, 3000, 4000, '[[116.3994,39.9113],[116.4004,39.9123]]', 1),
(4, '测试路段D', 4, 4000, 5000, '[[116.4004,39.9123],[116.4014,39.9133]]', 1),
(5, '测试路段E', 3, 5000, 6000, '[[116.4014,39.9133],[116.4024,39.9143]]', 0);

-- =====================================================
-- 2. 插入驾驶异常事件测试数据 (DRIVE)
-- =====================================================

-- 超速事件
INSERT INTO drive_event (event_id, event_type, car_id, car_num, start_mil, end_mil, start_time, end_time, 
                        dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_DRIVE_001', 'OVER_SPEED', 'CAR001', '京A12345', 1100, 1200, '2025-08-15 08:30:00', '2025-08-15 08:32:00', 
 NULL, 0, 1, 'UP', 1),
('TEST_DRIVE_002', 'OVER_SPEED', 'CAR002', '京B67890', 2100, 2150, '2025-08-15 09:15:00', '2025-08-15 09:16:30', 
 '2025-08-15 09:20:00', 1, 2, 'DOWN', 2),
('TEST_DRIVE_003', 'OVER_SPEED', 'CAR003', '京C11111', 3200, 3300, '2025-08-16 10:45:00', '2025-08-16 10:47:00', 
 NULL, 0, 3, 'UP', 3);

-- 逆行事件
INSERT INTO drive_event (event_id, event_type, car_id, car_num, start_mil, end_mil, start_time, end_time, 
                        dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_DRIVE_004', 'BACK_DRIVE', 'CAR004', '京D22222', 1500, 1400, '2025-08-16 14:20:00', '2025-08-16 14:22:00', 
 '2025-08-16 14:25:00', 1, 1, 'DOWN', 2),
('TEST_DRIVE_005', 'BACK_DRIVE', 'CAR005', '京E33333', 4200, 4100, '2025-08-17 16:30:00', '2025-08-17 16:32:00', 
 NULL, 0, 4, 'UP', 1);

-- 应急车道占用事件
INSERT INTO drive_event (event_id, event_type, car_id, car_num, start_mil, end_mil, start_time, end_time, 
                        dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_DRIVE_006', 'EMERGENCY_LANE', 'CAR006', '京F44444', 1800, 1850, '2025-08-17 11:00:00', '2025-08-17 11:05:00', 
 '2025-08-17 11:10:00', 1, 1, 'UP', 5),
('TEST_DRIVE_007', 'EMERGENCY_LANE', 'CAR007', '京G55555', 3500, 3600, '2025-08-18 13:45:00', '2025-08-18 13:50:00', 
 NULL, 0, 3, 'DOWN', 6);

-- 低速行驶事件
INSERT INTO drive_event (event_id, event_type, car_id, car_num, start_mil, end_mil, start_time, end_time, 
                        dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_DRIVE_008', 'LOW_SPEED', 'CAR008', '京H66666', 2300, 2500, '2025-08-18 07:30:00', '2025-08-18 07:35:00', 
 NULL, 0, 2, 'UP', 1),
('TEST_DRIVE_009', 'LOW_SPEED', 'CAR009', '京J77777', 4500, 4700, '2025-08-19 15:20:00', '2025-08-19 15:25:00', 
 '2025-08-19 15:30:00', 1, 4, 'DOWN', 2);

-- 违法停车事件
INSERT INTO drive_event (event_id, event_type, car_id, car_num, start_mil, end_mil, start_time, end_time, 
                        dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_DRIVE_010', 'STOP', 'CAR010', '京K88888', 5200, 5200, '2025-08-19 12:15:00', '2025-08-19 12:25:00', 
 '2025-08-19 12:30:00', 1, 5, 'UP', 2),
('TEST_DRIVE_011', 'STOP', 'CAR011', '京L99999', 1600, 1600, '2025-08-20 09:40:00', '2025-08-20 09:50:00', 
 NULL, 0, 1, 'DOWN', 3);

-- =====================================================
-- 3. 插入通行障碍事件测试数据 (PASS)
-- =====================================================

-- 行人入侵事件
INSERT INTO pass_event (event_id, event_type, start_mil, end_mil, start_time, end_time, 
                       dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_PASS_001', 'PEDESTRIAN', 1300, 1320, '2025-08-15 06:45:00', '2025-08-15 06:47:00', 
 '2025-08-15 06:50:00', 1, 1, 'UP', 9),
('TEST_PASS_002', 'PEDESTRIAN', 2800, 2850, '2025-08-16 18:30:00', '2025-08-16 18:33:00', 
 NULL, 0, 2, 'DOWN', 9),
('TEST_PASS_003', 'PEDESTRIAN', 3700, 3750, '2025-08-17 20:15:00', '2025-08-17 20:18:00', 
 '2025-08-17 20:25:00', 1, 3, 'UP', 9),
('TEST_PASS_004', 'PEDESTRIAN', 4800, 4820, '2025-08-18 22:00:00', '2025-08-18 22:02:00', 
 NULL, 0, 4, 'DOWN', 9);

-- 抛洒物事件
INSERT INTO pass_event (event_id, event_type, start_mil, end_mil, start_time, end_time, 
                       dispose_time, dispose_status, segment_id, direction, wn) VALUES
('TEST_PASS_005', 'SPRINKLE', 1700, 1750, '2025-08-16 08:20:00', '2025-08-16 08:25:00', 
 '2025-08-16 08:35:00', 1, 1, 'DOWN', 2),
('TEST_PASS_006', 'SPRINKLE', 2400, 2450, '2025-08-17 14:10:00', '2025-08-17 14:15:00', 
 NULL, 0, 2, 'UP', 1),
('TEST_PASS_007', 'SPRINKLE', 3400, 3500, '2025-08-18 16:45:00', '2025-08-18 16:50:00', 
 '2025-08-18 17:00:00', 1, 3, 'DOWN', 3),
('TEST_PASS_008', 'SPRINKLE', 5500, 5600, '2025-08-19 11:30:00', '2025-08-19 11:40:00', 
 NULL, 0, 5, 'UP', 2);

-- =====================================================
-- 4. 插入流量异常事件测试数据 (TRAFFIC)
-- =====================================================

-- 主线拥堵事件
INSERT INTO traffic_event (event_id, event_type, direction, start_mil, end_mil, start_time, end_time, 
                          dispose_time, dispose_status, segment_id) VALUES
('TEST_TRAFFIC_001', 'CONGESTION_MAIN', 'UP', 1000, 1500, '2025-08-15 07:30:00', '2025-08-15 08:15:00', 
 '2025-08-15 08:30:00', 1, 1),
('TEST_TRAFFIC_002', 'CONGESTION_MAIN', 'DOWN', 2000, 2800, '2025-08-16 17:45:00', '2025-08-16 18:30:00', 
 NULL, 0, 2),
('TEST_TRAFFIC_003', 'CONGESTION_MAIN', 'UP', 3200, 3900, '2025-08-17 08:00:00', '2025-08-17 09:00:00', 
 '2025-08-17 09:15:00', 1, 3),
('TEST_TRAFFIC_004', 'CONGESTION_MAIN', 'DOWN', 4100, 4800, '2025-08-18 18:00:00', '2025-08-18 19:15:00', 
 NULL, 0, 4),
('TEST_TRAFFIC_005', 'CONGESTION_MAIN', 'UP', 5100, 5800, '2025-08-19 07:45:00', '2025-08-19 08:45:00', 
 '2025-08-19 09:00:00', 1, 5),
('TEST_TRAFFIC_006', 'CONGESTION_MAIN', 'DOWN', 1200, 1800, '2025-08-20 17:30:00', '2025-08-20 18:45:00', 
 NULL, 0, 1);

-- =====================================================
-- 验证数据插入结果
-- =====================================================

-- 查看插入的数据统计
SELECT '驾驶异常事件' as event_category, COUNT(*) as count FROM drive_event WHERE event_id LIKE 'TEST_%'
UNION ALL
SELECT '通行障碍事件' as event_category, COUNT(*) as count FROM pass_event WHERE event_id LIKE 'TEST_%'
UNION ALL
SELECT '流量异常事件' as event_category, COUNT(*) as count FROM traffic_event WHERE event_id LIKE 'TEST_%'
UNION ALL
SELECT '测试路段' as event_category, COUNT(*) as count FROM road_segment WHERE name LIKE '测试路段%';

-- =====================================================
-- 测试查询示例
-- =====================================================

-- 查询超速事件
-- SELECT * FROM drive_event WHERE event_type = 'OVER_SPEED' AND event_id LIKE 'TEST_%';

-- 查询行人入侵事件
-- SELECT * FROM pass_event WHERE event_type = 'PEDESTRIAN' AND event_id LIKE 'TEST_%';

-- 查询主线拥堵事件
-- SELECT * FROM traffic_event WHERE event_type = 'CONGESTION_MAIN' AND event_id LIKE 'TEST_%';

-- 查询指定时间范围的事件
-- SELECT * FROM drive_event WHERE start_time >= '2025-08-15 00:00:00' AND start_time <= '2025-08-16 23:59:59' AND event_id LIKE 'TEST_%';

-- 查询指定路段的事件
-- SELECT * FROM drive_event WHERE segment_id = 1 AND event_id LIKE 'TEST_%';
